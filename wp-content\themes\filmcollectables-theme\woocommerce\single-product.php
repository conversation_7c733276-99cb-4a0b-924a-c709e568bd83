<?php
/**
 * The Template for displaying all single products
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product.php.
 */

defined( 'ABSPATH' ) || exit;

get_header();

while ( have_posts() ) :
    the_post();
    $product = wc_get_product();
    $ticket_price = $product ? $product->get_price() : '0.99';
    // Check if this product is configured as a competition
    $is_competition = get_post_meta(get_the_ID(), '_is_competition', true);
    
    if ($is_competition !== 'yes') {
        echo '<div style="text-align: center; padding: 2rem; color: #ef4444;">Error: This product is not configured as a competition. Please contact support.</div>';
        get_footer();
        return;
    }
    
    // For this system, the product ID IS the competition ID
    $competition_id = get_the_ID();
    
    $max_tickets = get_post_meta(get_the_ID(), '_competition_max_tickets', true);
    $max_tickets = empty($max_tickets) ? 999 : intval($max_tickets);

    // Get sold tickets count - sync with WooCommerce stock if needed
    $sold_tickets_raw = get_post_meta(get_the_ID(), '_competition_sold_tickets', true);
    $sold_tickets = empty($sold_tickets_raw) ? 0 : intval($sold_tickets_raw);

    // Check if sold tickets count is out of sync with WooCommerce stock
    $wc_stock = get_post_meta(get_the_ID(), '_stock', true);
    $wc_stock = is_numeric($wc_stock) ? intval($wc_stock) : $max_tickets;
    $calculated_sold = $max_tickets - $wc_stock;

    // If the calculated sold tickets don't match the stored value, sync them
    if ($calculated_sold !== $sold_tickets && $calculated_sold >= 0) {
        $sold_tickets = $calculated_sold;
        update_post_meta(get_the_ID(), '_competition_sold_tickets', $sold_tickets);
    }

    $start_date = get_post_meta(get_the_ID(), '_competition_start_date', true);
    $end_date = get_post_meta(get_the_ID(), '_competition_end_date', true);
    $progress_percentage = $max_tickets > 0 ? min(100, ($sold_tickets / $max_tickets) * 100) : 0;
    $remaining_tickets = max(0, $max_tickets - $sold_tickets);



    // Get competition status using the proper function
    $status = get_competition_status_simple(get_the_ID());
    
    // Check competition status
    $current_time = current_time('timestamp');
    $is_scheduled = ($status === 'scheduled');
    $is_ended = in_array($status, array('awaiting_draw', 'completed'));
    $is_active = ($status === 'active');
    $question = get_post_meta(get_the_ID(), '_competition_question', true);
    $answer_1 = get_post_meta(get_the_ID(), '_competition_answer_1', true);
    $answer_2 = get_post_meta(get_the_ID(), '_competition_answer_2', true);
    $answer_3 = get_post_meta(get_the_ID(), '_competition_answer_3', true);
    $correct_answer = get_post_meta(get_the_ID(), '_competition_correct_answer', true) ?: 1;

    $current_question = null;
    if ($question && $answer_1 && $answer_2 && $answer_3) {
        $current_question = array(
            'question' => $question,
            'answers' => array($answer_1, $answer_2, $answer_3),
            'correct_answer' => $correct_answer
        );
    }

    // Get instant wins data
    $instant_wins_enabled = get_post_meta(get_the_ID(), '_instant_wins_enabled', true);
    $instant_win_prizes = get_post_meta(get_the_ID(), '_instant_win_prizes', true) ?: array();

    // Get claimed instant wins for this competition
    global $wpdb;
    $instant_wins_table = $wpdb->prefix . 'competition_instant_wins';
    $claimed_instant_wins = $wpdb->get_results($wpdb->prepare(
        "SELECT ticket_number, prize_name FROM $instant_wins_table WHERE competition_id = %d",
        get_the_ID()
    ));
?>

<!-- Modern Competition Page Design -->
<div style="min-height: 100vh; background: #000; color: white;">

    <!-- Hero Section -->
    <section style="background: linear-gradient(to right, #000, #1f2937, #000); padding: 2rem 0;">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1rem;">
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="font-size: clamp(2rem, 5vw, 4rem); font-weight: bold; margin-bottom: 1rem; background: linear-gradient(to right, #0ea5e9, #3b82f6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; line-height: 1.2;">
                    <?php echo esc_html(strtoupper(get_the_title())); ?>
                </h1>
                <div style="display: flex; justify-content: center; flex-wrap: wrap; gap: 1rem; font-size: 0.875rem;">
                    <?php if ($is_active) : ?>
                    <div style="display: flex; align-items: center; gap: 0.5rem; background: rgba(6, 182, 212, 0.1); padding: 0.5rem 1rem; border-radius: 0.5rem; border: 1px solid rgba(6, 182, 212, 0.2);">
                        <span style="font-size: 1rem;">🎫</span>
                        <span><?php echo number_format($remaining_tickets); ?> TICKETS REMAINING</span>
                    </div>
                    <?php elseif ($status === 'awaiting_draw') : ?>
                    <div style="display: flex; align-items: center; gap: 0.5rem; background: rgba(251, 146, 60, 0.1); padding: 0.5rem 1rem; border-radius: 0.5rem; border: 1px solid rgba(251, 146, 60, 0.2);">
                        <span style="font-size: 1rem;">⏳</span>
                        <span>AWAITING DRAW</span>
                    </div>
                    <?php elseif ($status === 'completed') : ?>
                    <div style="display: flex; align-items: center; gap: 0.5rem; background: rgba(139, 92, 246, 0.1); padding: 0.5rem 1rem; border-radius: 0.5rem; border: 1px solid rgba(139, 92, 246, 0.2);">
                        <span style="font-size: 1rem;">✅</span>
                        <span>WINNER SELECTED</span>
                    </div>
                    <?php endif; ?>
                    <?php if ($ticket_price == 0 || $ticket_price == '0.00') : ?>
                    <div style="display: flex; align-items: center; gap: 0.5rem; background: rgba(59, 130, 246, 0.1); padding: 0.5rem 1rem; border-radius: 0.5rem; border: 1px solid rgba(59, 130, 246, 0.2);">
                        <span style="font-size: 1rem;">👤</span>
                        <span>ONE FREE ENTRY PER PERSON</span>
                    </div>
                    <?php else : ?>
                    <div style="display: flex; align-items: center; gap: 0.5rem; background: rgba(59, 130, 246, 0.1); padding: 0.5rem 1rem; border-radius: 0.5rem; border: 1px solid rgba(59, 130, 246, 0.2);">
                        <span style="font-size: 1rem;">💷</span>
                        <span>£<?php echo number_format($ticket_price, 2); ?> PER TICKET</span>
                    </div>
                    <?php endif; ?>
                    <?php if ($end_date) : ?>
                    <div style="display: flex; align-items: center; gap: 0.5rem; background: rgba(147, 51, 234, 0.1); padding: 0.5rem 1rem; border-radius: 0.5rem; border: 1px solid rgba(147, 51, 234, 0.2);">
                        <span style="font-size: 1rem;">🎁</span>
                        <span>LIVE DRAW <?php echo esc_html(strtoupper(date('jS F Y @ g:i A', strtotime($end_date)))); ?></span>
                    </div>
                    <?php else : ?>
                    <div style="display: flex; align-items: center; gap: 0.5rem; background: rgba(147, 51, 234, 0.1); padding: 0.5rem 1rem; border-radius: 0.5rem; border: 1px solid rgba(147, 51, 234, 0.2);">
                        <span style="font-size: 1rem;">🎁</span>
                        <span>LIVE DRAW 14TH JULY 2025 @ 6:00 PM</span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Two Column Layout: Gallery Left, Entry Form Right -->
            <div class="product-main-layout" style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; align-items: start;">

                <!-- Left Column - Prize Gallery -->
                <div style="display: flex; flex-direction: column; gap: 1.5rem;">
                    <!-- Main Featured Prize -->
                    <div style="position: relative; group;">
                        <?php if (has_post_thumbnail()) : ?>
                            <img id="main-gallery-image"
                                 src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'large'); ?>"
                                 alt="<?php the_title(); ?>"
                                 style="width: 100%; height: auto; border-radius: 0.5rem; border: 1px solid rgba(6, 182, 212, 0.2); transition: border-color 0.3s; cursor: pointer;"
                                 onclick="openLightbox(0)">
                        <?php else : ?>
                            <div style="width: 100%; height: 400px; background: #1f2937; border-radius: 0.5rem; border: 1px solid rgba(6, 182, 212, 0.2); display: flex; align-items: center; justify-content: center; color: #6b7280;">
                                Prize Image
                            </div>
                        <?php endif; ?>
                        <!-- Removed instant win banner from gallery images -->
                    </div>

                    <!-- Gallery Thumbnails -->
                    <div class="gallery-thumbnails" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 0.75rem;">
                        <?php
                        // Get actual gallery images from product
                        $product = wc_get_product(get_the_ID());
                        $gallery_image_ids = $product->get_gallery_image_ids();

                        // Include featured image as first image
                        $all_image_ids = array();
                        if (has_post_thumbnail()) {
                            $all_image_ids[] = get_post_thumbnail_id();
                        }
                        $all_image_ids = array_merge($all_image_ids, $gallery_image_ids);

                        // Limit to 8 images and create gallery array for lightbox
                        $gallery_images = array();
                        foreach (array_slice($all_image_ids, 0, 8) as $index => $image_id) {
                            $image_url = wp_get_attachment_image_url($image_id, 'medium');
                            $large_image_url = wp_get_attachment_image_url($image_id, 'large');
                            $gallery_images[] = array(
                                'thumb' => $image_url,
                                'large' => $large_image_url,
                                'alt' => get_post_meta($image_id, '_wp_attachment_image_alt', true) ?: get_the_title()
                            );
                        }

                        foreach ($gallery_images as $index => $image) : ?>
                        <div style="position: relative; cursor: pointer;" onclick="openLightbox(<?php echo $index; ?>)">
                            <img src="<?php echo esc_url($image['thumb']); ?>"
                                 alt="<?php echo esc_attr($image['alt']); ?>"
                                 style="width: 100%; height: auto; border-radius: 0.5rem; border: 1px solid rgba(6, 182, 212, 0.2); transition: border-color 0.3s;"
                                 onmouseover="this.style.borderColor='#0ea5e9'; this.style.opacity='0.8';"
                                 onmouseout="this.style.borderColor='rgba(6, 182, 212, 0.2)'; this.style.opacity='1';">
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Right Column - Entry Form -->
                <div>
                    <div style="background: rgba(0, 0, 0, 0.8); border: 1px solid rgba(6, 182, 212, 0.2); border-radius: 0.5rem; padding: 2rem;">

                        <?php if ($is_active) : ?>
                        <!-- Price Display -->
                        <div style="text-align: center; margin-bottom: 2rem;">
                            <?php if ($ticket_price == 0 || $ticket_price == '0.00') : ?>
                            <div id="total-price" style="font-size: 3.75rem; font-weight: bold; color: #10b981; margin-bottom: 0.5rem;">
                                FREE
                            </div>
                            <div style="color: #9ca3af;">
                                for <span id="entry-count">1</span> <span id="entry-text">entry</span>
                            </div>
                            <?php else : ?>
                            <div id="total-price" style="font-size: 3.75rem; font-weight: bold; color: #0ea5e9; margin-bottom: 0.5rem;">
                                £<span id="price-amount"><?php echo number_format($ticket_price * 5, 2); ?></span>
                            </div>
                            <div style="color: #9ca3af;">
                                for <span id="entry-count">5</span> <span id="entry-text">entries</span>
                            </div>
                            <?php endif; ?></div>

                        <!-- Entry Method Tabs -->
                        <div style="margin-bottom: 2rem;">
                            <!-- Tab Navigation -->
                            <div style="display: flex; border-bottom: 2px solid #374151; margin-bottom: 1.5rem;">
                                <button type="button" id="paid-entry-tab" class="entry-tab active" style="flex: 1; padding: 1rem; background: none; border: none; color: #0ea5e9; font-weight: 500; cursor: pointer; border-bottom: 2px solid #0ea5e9; transition: all 0.3s;">
                                    💷 Paid Entry
                                </button>
                                <button type="button" id="free-entry-tab" class="entry-tab" style="flex: 1; padding: 1rem; background: none; border: none; color: #9ca3af; font-weight: 500; cursor: pointer; border-bottom: 2px solid transparent; transition: all 0.3s;">
                                    📮 Free Entry
                                </button>
                            </div>

                            <!-- Paid Entry Tab Content -->
                            <div id="paid-entry-content" class="tab-content" style="display: block;">
                                <!-- Ticket Slider with Plus/Minus -->
                                <div style="margin-bottom: 1rem;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                        <label style="color: #0ea5e9; font-weight: 500;">Number of Tickets</label>
                                        <span id="ticket-display" style="font-size: 1.5rem; font-weight: bold; color: #0ea5e9;">5</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 1rem;">
                                        <button type="button"
                                                id="decrease-tickets"
                                                style="width: 40px; height: 40px; background: #374151; color: white; border: 1px solid #0ea5e9; border-radius: 50%; font-size: 1.5rem; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s;"
                                                onmouseover="this.style.background='#0ea5e9'; this.style.color='#000';"
                                                onmouseout="this.style.background='#374151'; this.style.color='white';">
                                            −
                                        </button>
                                        <input type="range"
                                               id="ticket-slider"
                                               min="1"
                                               max="<?php echo min(999, $remaining_tickets); ?>"
                                               value="<?php echo min(5, $remaining_tickets); ?>"
                                               style="flex: 1; height: 8px; background: #374151; border-radius: 5px; outline: none; -webkit-appearance: none;">
                                        <button type="button"
                                                id="increase-tickets"
                                                style="width: 40px; height: 40px; background: #374151; color: white; border: 1px solid #0ea5e9; border-radius: 50%; font-size: 1.5rem; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s;"
                                                onmouseover="this.style.background='#0ea5e9'; this.style.color='#000';"
                                                onmouseout="this.style.background='#374151'; this.style.color='white';">
                                            +
                                        </button>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; font-size: 0.875rem; color: #9ca3af; margin-top: 0.5rem;">
                                        <span>1</span>
                                        <span><?php echo min(999, $remaining_tickets); ?></span>
                                    </div>
                                </div>

                                <!-- Preset Buttons -->
                                <div class="preset-buttons" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 1rem; margin-bottom: 1.5rem;">
                                    <?php
                                    $preset_values = array(5, 10, 15, 25);
                                    foreach ($preset_values as $preset) :
                                        if ($preset <= min(999, $remaining_tickets)) :
                                    ?>
                                    <button type="button" class="preset-btn" data-value="<?php echo $preset; ?>"
                                            style="border: 1px solid rgba(6, 182, 212, 0.2); background: transparent; color: white; padding: 0.5rem; border-radius: 0.25rem; cursor: pointer; transition: all 0.3s;"
                                            onmouseover="this.style.borderColor='#0ea5e9'; this.style.background='rgba(6, 182, 212, 0.1)';"
                                            onmouseout="this.style.borderColor='rgba(6, 182, 212, 0.2)'; this.style.background='transparent';">
                                        <?php echo $preset; ?>
                                    </button>
                                    <?php
                                        endif;
                                    endforeach;
                                    ?>
                                </div>
                            </div>

                            <!-- Free Entry Tab Content -->
                            <div id="free-entry-content" class="tab-content" style="display: none;">
                                <div style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.2); border-radius: 0.5rem; padding: 1.5rem; margin-bottom: 1.5rem;">
                                    <div style="text-align: center; margin-bottom: 1rem;">
                                        <div style="font-size: 1.5rem; color: #10b981; font-weight: bold; margin-bottom: 0.5rem;">
                                            📮 FREE POSTAL ENTRY
                                        </div>
                                        <div style="color: #9ca3af; font-size: 0.875rem;">
                                            Enter for free by post - One entry per person
                                        </div>
                                    </div>

                                    <div style="font-size: 0.9rem; line-height: 1.6; color: #d1d5db;">
                                        <p style="margin-bottom: 1rem;">
                                            You may enter this competition for free by sending your entry on an unenclosed postcard by first or second class post to:
                                        </p>

                                        <div style="background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.2); padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem; text-align: center; font-weight: bold; color: #0ea5e9;">
                                            FILM COLLECTABLES COMPETITIONS LIMITED<br>
                                            Ground Floor, Gallery Building<br>
                                            65-69 Dublin Rd<br>
                                            Belfast<br>
                                            BT2 7HG
                                        </div>

                                        <p style="margin-bottom: 0.5rem; font-weight: 500; color: #0ea5e9;">
                                            Please include the following information:
                                        </p>
                                        <ul style="list-style: none; padding-left: 0; margin-bottom: 1rem;">
                                            <li style="margin-bottom: 0.25rem;">• Your full name</li>
                                            <li style="margin-bottom: 0.25rem;">• Your complete address including postcode</li>
                                            <li style="margin-bottom: 0.25rem;">• Your phone number</li>
                                            <li style="margin-bottom: 0.25rem;">• Competition name: "<?php echo get_the_title(); ?>"</li>
                                            <li style="margin-bottom: 0.25rem;">• Answer to the skill question (see below)</li>
                                        </ul>

                                        <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.2); padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem;">
                                            <p style="margin: 0; font-size: 0.875rem; color: #ef4444;">
                                                <strong>Important:</strong> Postal entries must be received before the competition closing date. Only one postal entry per person per competition is allowed.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tickets Progress Bar -->
                        <div style="margin: 1.5rem 0; background: rgba(17, 24, 39, 0.8); border: 1px solid rgba(6, 182, 212, 0.2); border-radius: 0.75rem; padding: 1.5rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                <div style="text-align: left;">
                                    <div style="font-size: 1.25rem; font-weight: bold; color: #0ea5e9;"><?php echo number_format($sold_tickets); ?> sold</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 1.25rem; font-weight: bold; color: #0ea5e9;"><?php echo number_format($remaining_tickets); ?> remaining</div>
                                </div>
                            </div>
                            <div style="background: #374151; border-radius: 0.5rem; height: 1rem; overflow: hidden; position: relative;">
                                <div style="background: linear-gradient(to right, #0ea5e9, #3b82f6); height: 100%; width: <?php echo $progress_percentage; ?>%; transition: width 0.3s ease;"></div>
                            </div>
                            <div style="text-align: center; margin-top: 0.75rem; font-size: 0.875rem; color: #9ca3af;">
                                <?php echo number_format($progress_percentage, 1); ?>% sold
                            </div>
                        </div>

                        <!-- Skill Question Section -->
                        <?php if ($current_question && $is_active) : ?>
                        <div style="margin-bottom: 1.5rem;">
                            <label style="color: #0ea5e9; margin-bottom: 1rem; display: block; font-weight: 500;">Please pick one of your answers</label>
                            <div style="font-size: 1.125rem; margin-bottom: 1rem; color: white;">
                                <?php echo esc_html($current_question['question']); ?>
                            </div>
                            <div id="skill-question" style="display: flex; flex-direction: column; gap: 0.75rem;">
                                <?php foreach ($current_question['answers'] as $index => $answer) : ?>
                                <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; border-radius: 0.5rem; border: 1px solid #374151; transition: all 0.3s; cursor: pointer;"
                                     class="answer-option"
                                     data-answer="<?php echo $index; ?>"
                                     data-correct="<?php echo ($index + 1) == $current_question['correct_answer'] ? 'true' : 'false'; ?>"
                                     onmouseover="if (!this.querySelector('input[type=radio]').disabled && this.style.borderColor !== '#ef4444') { this.style.borderColor='rgba(6, 182, 212, 0.5)'; }"
                                     onmouseout="if (!this.querySelector('input[type=radio]').disabled && !this.querySelector('input[type=radio]').checked && this.style.borderColor !== '#ef4444') { this.style.borderColor='#374151'; }">
                                    <input type="radio"
                                           name="skill_answer"
                                           value="<?php echo $index; ?>"
                                           id="answer_<?php echo $index; ?>"
                                           style="accent-color: #0ea5e9;">
                                    <label for="answer_<?php echo $index; ?>" style="cursor: pointer; color: white;">
                                        <?php echo esc_html(strtoupper($answer)); ?>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <!-- Question Feedback -->
                            <div id="question-feedback" style="margin-top: 1rem; padding: 0.75rem; border-radius: 0.5rem; display: none; text-align: center; font-weight: 500;">
                                <!-- Feedback will be shown here -->
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Add to Basket Button -->
                        <button type="button"
                                id="add-to-basket-btn"
                                <?php echo $current_question ? 'disabled' : ''; ?>
                                style="width: 100%; background: linear-gradient(to right, #0ea5e9, #3b82f6); color: #000; font-weight: bold; padding: 1rem; font-size: 1.125rem; border: none; border-radius: 0.25rem; cursor: pointer; transition: all 0.3s; <?php echo $current_question ? 'opacity: 0.5; cursor: not-allowed;' : ''; ?>"
                                onmouseover="if (!this.disabled) { this.style.background = 'linear-gradient(to right, #0284c7, #2563eb)'; }"
                                onmouseout="if (!this.disabled) { this.style.background = 'linear-gradient(to right, #0ea5e9, #3b82f6)'; }">
                            ADD TICKETS TO BASKET
                        </button>


                        
                        <?php elseif ($status === 'awaiting_draw') : ?>
                        <!-- Awaiting Draw State -->
                        <div style="text-align: center;">
                            <div style="font-size: 4rem; margin-bottom: 1rem;">⏳</div>
                            <h3 style="color: #f59e0b; font-size: 1.875rem; font-weight: bold; margin-bottom: 1rem;">AWAITING DRAW</h3>
                            <p style="color: #fed7aa; font-size: 1.125rem; margin-bottom: 1.5rem;">This competition has ended</p>
                            <div style="background: rgba(254, 243, 199, 0.1); border: 1px solid rgba(251, 146, 60, 0.3); border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem;">
                                <p style="color: #fb923c; margin: 0;">The winner will be drawn soon!</p>
                            </div>
                            <?php if ($end_date) : ?>
                            <p style="color: #9ca3af; font-size: 0.875rem;">Competition ended: <?php echo date('jS F Y @ g:i A', strtotime($end_date)); ?></p>
                            <?php endif; ?>
                        </div>
                        
                        <?php elseif ($status === 'completed') : ?>
                        <!-- Competition Completed State -->
                        <?php
                        $winner_name = get_post_meta(get_the_ID(), 'winner_username', true);
                        $winner_ticket = get_post_meta(get_the_ID(), 'winner_ticket_number', true);
                        ?>
                        <div style="text-align: center;">
                            <div style="font-size: 4rem; margin-bottom: 1rem;">🏆</div>
                            <h3 style="color: #8b5cf6; font-size: 1.875rem; font-weight: bold; margin-bottom: 1rem;">WINNER SELECTED</h3>

                            <?php if ($winner_name && $winner_ticket) : ?>
                                <div style="background: rgba(243, 232, 255, 0.1); border: 1px solid rgba(139, 92, 246, 0.3); border-radius: 0.5rem; padding: 1.5rem; margin-bottom: 1rem;">
                                    <div style="font-size: 1.25rem; font-weight: bold; color: #8b5cf6; margin-bottom: 0.5rem;">
                                        🎉 <?php echo esc_html($winner_name); ?>
                                    </div>
                                    <div style="color: #a78bfa; font-size: 1rem;">
                                        Winning Ticket: #<?php echo esc_html($winner_ticket); ?>
                                    </div>
                                </div>
                            <?php else : ?>
                                <div style="background: rgba(243, 232, 255, 0.1); border: 1px solid rgba(139, 92, 246, 0.3); border-radius: 0.5rem; padding: 1rem;">
                                    <p style="color: #a78bfa; margin: 0;">This competition has been completed and the winner has been announced.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php else : ?>
                        <!-- Default ended state -->
                        <div style="text-align: center;">
                            <div style="font-size: 4rem; margin-bottom: 1rem;">⏰</div>
                            <h3 style="color: #ef4444; font-size: 1.875rem; font-weight: bold; margin-bottom: 1rem;">COMPETITION ENDED</h3>
                            <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 0.5rem; padding: 1rem;">
                                <p style="color: #fca5a5; margin: 0;">This competition has ended.</p>
                            </div>
                        </div>
                        
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Prize Description Section -->
    <section style="padding: 3rem 0; background: #000;">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1rem;">
            <div style="max-width: 4xl; margin: 0 auto;">
                <h2 style="font-size: 1.875rem; font-weight: bold; text-align: center; margin-bottom: 2rem; color: #0ea5e9;">
                    <?php echo esc_html(strtoupper(get_the_title())); ?>
                </h2>
                <div style="text-align: center; color: #d1d5db;">
                    <p style="font-size: 1.25rem; color: #0ea5e9; margin-bottom: 1rem;">HAVE <?php echo $end_date ? strtoupper(human_time_diff(current_time('timestamp'), strtotime($end_date))) : '24 HOURS'; ?> TO ENTER TO WIN</p>
                    <p style="font-size: 1.125rem; margin-bottom: 0.5rem;">FANTASTIC FILM COLLECTABLES</p>
                    <?php if ($ticket_price == 0 || $ticket_price == '0.00') : ?>
                    <p style="color: #10b981; font-weight: bold; margin-bottom: 1rem;">FREE TO ENTER - ONE ENTRY PER PERSON</p>
                    <?php else : ?>
                    <p style="color: #0ea5e9; font-weight: bold; margin-bottom: 1rem;">TICKETS FROM £<?php echo number_format($ticket_price, 2); ?> EACH</p>
                    <?php endif; ?>
                    <div style="max-width: 800px; margin: 0 auto; line-height: 1.6;">
                        <?php
                        $content = get_the_content();
                        if ($content) {
                            echo wp_kses_post($content);
                        } else {
                            echo '<p>Amazing film collectables await and we need to find a new home for all of them! We have incredible prizes and we\'re giving away fantastic collectables to lucky winners.</p>';
                            echo '<p>Ticket numbers are randomly generated once you complete your purchase. If any of your numbers match those in the list below, you win that prize!</p>';
                            echo '<p>We will hold live draw where we announce if anyone has won the jackpot for this competition.</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
            </div>
        </div>

    <!-- Instant Win Prizes Section -->
    <?php if ($instant_wins_enabled && !empty($instant_win_prizes)) : ?>
    <section style="padding: 3rem 0; background: rgba(31, 41, 55, 0.3);">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1rem;">
            <h2 style="font-size: 1.875rem; font-weight: bold; text-align: center; margin-bottom: 3rem; color: #0ea5e9;">INSTANT WIN PRIZES</h2>
            <div style="max-width: 4xl; margin: 0 auto; display: flex; flex-direction: column; gap: 1.5rem;">
                <?php foreach ($instant_win_prizes as $index => $prize) :
                    // Check if this prize has been claimed
                    $prize_ticket_numbers = $prize['ticket_numbers'] ?? array();
                    $claimed_count = 0;

                    foreach ($claimed_instant_wins as $claimed_win) {
                        if (in_array($claimed_win->ticket_number, $prize_ticket_numbers)) {
                            $claimed_count++;
                        }
                    }

                    $total_available = count($prize_ticket_numbers);
                    $remaining = $total_available - $claimed_count;
                    $is_claimed = $remaining <= 0;
                ?>
                <div style="background: rgba(0, 0, 0, 0.6); border: 1px solid <?php echo $is_claimed ? 'rgba(239, 68, 68, 0.3)' : 'rgba(6, 182, 212, 0.2)'; ?>; border-radius: 0.5rem; padding: 1.5rem; transition: border-color 0.3s; <?php echo $is_claimed ? 'opacity: 0.7;' : ''; ?>"
                     onmouseover="<?php echo $is_claimed ? '' : "this.style.borderColor='#0ea5e9';"; ?>"
                     onmouseout="<?php echo $is_claimed ? '' : "this.style.borderColor='rgba(6, 182, 212, 0.2)';"; ?>">
                    <div style="display: flex; align-items: center; gap: 1.5rem;">
                        <?php if (!empty($prize['image'])) : ?>
                        <img src="<?php echo esc_url($prize['image']); ?>"
                             alt="<?php echo esc_attr($prize['name']); ?>"
                             style="width: 100px; height: 120px; object-fit: cover; border-radius: 0.5rem; border: 1px solid rgba(6, 182, 212, 0.2); <?php echo $is_claimed ? 'filter: grayscale(100%);' : ''; ?>">
                        <?php else : ?>
                        <div style="width: 100px; height: 120px; background: #374151; border-radius: 0.5rem; border: 1px solid rgba(6, 182, 212, 0.2); display: flex; align-items: center; justify-content: center; color: #9ca3af; font-size: 2rem; position: relative;">
                            🎁
                            <?php if ($is_claimed) : ?>
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(239, 68, 68, 0.9); color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: bold;">CLAIMED</div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                        <div style="flex: 1;">
                            <h3 style="font-size: 1.125rem; font-weight: 600; color: <?php echo $is_claimed ? '#ef4444' : '#0ea5e9'; ?>; margin-bottom: 0.5rem;">
                                <?php echo esc_html($prize['name']); ?>
                                <?php if ($is_claimed) : ?>
                                <span style="font-size: 0.875rem; font-weight: normal; color: #ef4444;"> - CLAIMED</span>
                                <?php endif; ?>
                            </h3>
                            <p style="color: #9ca3af; font-size: 0.875rem; margin: 0;">
                                <?php
                                if (!empty($prize['description'])) {
                                    echo esc_html($prize['description']);
                                } else {
                                    if ($is_claimed) {
                                        echo "0 of $total_available remaining";
                                    } else {
                                        echo "$remaining of $total_available remaining";
                                    }
                                }
                                ?>
                            </p>
                        </div>
                        <div style="text-align: right;">
                            <div style="background: linear-gradient(to right, #eab308, #ca8a04); color: #000; padding: 0.25rem 0.75rem; border-radius: 0.25rem; font-weight: bold;">
                                #<?php echo $index + 1; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Related Competitions Section -->
    <section style="padding: 3rem 0; background: #000;">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1rem;">
            <h2 style="font-size: 1.875rem; font-weight: bold; text-align: center; margin-bottom: 3rem; color: #0ea5e9;">CHECK OUT THESE OTHER COMPETITIONS</h2>
            <div class="related-competitions" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1.5rem;">
                <style>
                @media (max-width: 768px) {
                    .related-competitions {
                        grid-template-columns: 1fr !important;
                    }
                }
                @media (min-width: 769px) and (max-width: 1024px) {
                    .related-competitions {
                        grid-template-columns: repeat(2, 1fr) !important;
                    }
                }
                </style>
                <?php
                // Get related competitions - only show active ones
                $all_competitions = get_posts(array(
                    'post_type' => 'product',
                    'posts_per_page' => 20, // Get more to filter from
                    'post__not_in' => array(get_the_ID()),
                    'meta_query' => array(
                        array(
                            'key' => '_competition_max_tickets',
                            'compare' => 'EXISTS'
                        )
                    )
                ));

                // Filter to only show active competitions
                $related_competitions = array();
                foreach ($all_competitions as $comp) {
                    $status = get_competition_status_simple($comp->ID);
                    if ($status === 'active') {
                        $related_competitions[] = $comp;
                        // Stop when we have 4 active competitions
                        if (count($related_competitions) >= 4) {
                            break;
                        }
                    }
                }

                foreach ($related_competitions as $comp) :
                    $comp_product = wc_get_product($comp->ID);
                    $comp_price = $comp_product ? $comp_product->get_price() : '0.99';
                    $comp_end_date = get_post_meta($comp->ID, '_competition_end_date', true);
                    $time_left = $comp_end_date ? human_time_diff(current_time('timestamp'), strtotime($comp_end_date)) : '3 days';
                ?>
                <div style="background: rgba(31, 41, 55, 0.5); border: 1px solid rgba(6, 182, 212, 0.2); border-radius: 0.5rem; overflow: hidden; transition: border-color 0.3s;"
                     onmouseover="this.style.borderColor='#0ea5e9';"
                     onmouseout="this.style.borderColor='rgba(6, 182, 212, 0.2)';">
                    <div style="position: relative;">
                        <?php if (has_post_thumbnail($comp->ID)) : ?>
                            <img src="<?php echo get_the_post_thumbnail_url($comp->ID, 'medium'); ?>"
                                 alt="<?php echo esc_attr($comp->post_title); ?>"
                                 style="width: 100%; height: 200px; object-fit: cover;">
                        <?php else : ?>
                            <div style="width: 100%; height: 200px; background: #374151; display: flex; align-items: center; justify-content: center; color: #9ca3af;">
                                Competition Image
                            </div>
                        <?php endif; ?>
                        <div style="position: absolute; top: 1rem; right: 1rem;">
                            <div style="background: #0ea5e9; color: #000; padding: 0.25rem 0.75rem; border-radius: 0.25rem; font-size: 0.875rem; font-weight: bold; display: flex; align-items: center; gap: 0.25rem;">
                                <span>⏰</span>
                                <?php echo esc_html($time_left); ?>
                            </div>
                        </div>
                    </div>
                    <div style="padding: 1rem;">
                        <h3 style="font-weight: 600; color: #0ea5e9; margin-bottom: 0.5rem; font-size: 1rem;">
                            <?php echo esc_html($comp->post_title); ?>
                        </h3>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 1.5rem; font-weight: bold; color: #0ea5e9;">
                                <?php echo ($comp_price == 0 || $comp_price == '0.00') ? 'FREE' : '£' . esc_html($comp_price); ?>
                            </span>
                            <a href="<?php echo get_permalink($comp->ID); ?>"
                               style="background: linear-gradient(to right, #0ea5e9, #3b82f6); color: #000; padding: 0.5rem 1rem; border-radius: 0.25rem; text-decoration: none; font-weight: bold; font-size: 0.875rem; transition: all 0.3s;"
                               onmouseover="this.style.background='linear-gradient(to right, #0284c7, #2563eb)';"
                               onmouseout="this.style.background='linear-gradient(to right, #0ea5e9, #3b82f6)';">
                                ENTER NOW
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const paidTab = document.getElementById('paid-entry-tab');
    const freeTab = document.getElementById('free-entry-tab');
    const paidContent = document.getElementById('paid-entry-content');
    const freeContent = document.getElementById('free-entry-content');

    function switchTab(activeTab, inactiveTab, activeContent, inactiveContent) {
        // Update tab styles
        activeTab.style.color = '#0ea5e9';
        activeTab.style.borderBottomColor = '#0ea5e9';
        inactiveTab.style.color = '#9ca3af';
        inactiveTab.style.borderBottomColor = 'transparent';

        // Update content visibility
        activeContent.style.display = 'block';
        inactiveContent.style.display = 'none';
    }

    if (paidTab && freeTab) {
        paidTab.addEventListener('click', function() {
            switchTab(paidTab, freeTab, paidContent, freeContent);
        });

        freeTab.addEventListener('click', function() {
            switchTab(freeTab, paidTab, freeContent, paidContent);
        });
    }

    const ticketSlider = document.getElementById('ticket-slider');
    const ticketDisplay = document.getElementById('ticket-display');
    const priceAmount = document.getElementById('price-amount');
    const entryCount = document.getElementById('entry-count');
    const entryText = document.getElementById('entry-text');
    
    // Function to update cart count in header
    function updateCartCount() {
        fetch('<?php echo admin_url("admin-ajax.php"); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=get_cart_count&nonce=<?php echo wp_create_nonce("filmcollectables_nonce"); ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const count = data.data.count;
                const total = data.data.total;
                const cartCountElement = document.querySelector('.cart-count');
                const cartTotalElement = document.querySelector('.cart-total');
                
                if (count > 0) {
                    // Update cart count
                    if (cartCountElement) {
                        cartCountElement.textContent = count;
                    } else {
                        // Add cart count badge if it doesn't exist
                        const cartLink = document.querySelector('a[href*="cart"]');
                        if (cartLink) {
                            const badge = document.createElement('span');
                            badge.className = 'cart-count absolute -top-1 -right-1 bg-indigo-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center';
                            badge.textContent = count;
                            cartLink.appendChild(badge);
                        }
                    }
                    
                    // Update cart total
                    if (cartTotalElement) {
                        cartTotalElement.innerHTML = total;
                    } else {
                        // Add cart total if it doesn't exist
                        const cartLink = document.querySelector('a[href*="cart"]');
                        if (cartLink) {
                            const totalSpan = document.createElement('span');
                            totalSpan.className = 'cart-total';
                            totalSpan.innerHTML = total;
                            cartLink.appendChild(totalSpan);
                        }
                    }
                } else {
                    if (cartCountElement) {
                        cartCountElement.remove();
                    }
                    if (cartTotalElement) {
                        cartTotalElement.remove();
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error updating cart count:', error);
        });
    }
    const presetBtns = document.querySelectorAll('.preset-btn');
    const addToBasketBtn = document.getElementById('add-to-basket-btn');
    const skillQuestion = document.getElementById('skill-question');
    const decreaseBtn = document.getElementById('decrease-tickets');
    const increaseBtn = document.getElementById('increase-tickets');
    const questionFeedback = document.getElementById('question-feedback');

    const ticketPrice = <?php echo json_encode($ticket_price); ?>;
    const isFreeCompetition = false; // Always treat as paid since free entry is postal only
    let questionAnswered = false;
    let selectedAnswer = null;

    // Update price and display
    function updateDisplay() {
        if (isFreeCompetition) {
            // Free competitions always have 1 entry
            return;
        }
        
        const quantity = parseInt(ticketSlider.value);
        const totalPrice = (ticketPrice * quantity).toFixed(2);

        if (ticketDisplay) ticketDisplay.textContent = quantity;
        if (priceAmount) priceAmount.textContent = totalPrice;
        if (entryCount) entryCount.textContent = quantity;
        if (entryText) entryText.textContent = quantity === 1 ? 'entry' : 'entries';
    }

    // Slider functionality
    if (ticketSlider) {
        ticketSlider.addEventListener('input', updateDisplay);
        updateDisplay();
    }

    // Plus/Minus buttons
    if (decreaseBtn) {
        decreaseBtn.addEventListener('click', function() {
            const currentValue = parseInt(ticketSlider.value);
            const minValue = parseInt(ticketSlider.min);
            if (currentValue > minValue) {
                ticketSlider.value = currentValue - 1;
                updateDisplay();
            }
        });
    }

    if (increaseBtn) {
        increaseBtn.addEventListener('click', function() {
            const currentValue = parseInt(ticketSlider.value);
            const maxValue = parseInt(ticketSlider.max);
            if (currentValue < maxValue) {
                ticketSlider.value = currentValue + 1;
                updateDisplay();
            } else if (currentValue === maxValue && maxValue === 999) {
                // Show tooltip about multiple purchases
                showTooltip(this, 'Want more? Complete this purchase and buy again!');
            }
        });
    }

    // Preset buttons
    presetBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const value = parseInt(this.dataset.value);
            if (ticketSlider) {
                ticketSlider.value = value;
                updateDisplay();
            }
        });
    });

    // Skill question handling
    if (skillQuestion) {
        const answerOptions = skillQuestion.querySelectorAll('.answer-option');

        answerOptions.forEach((option, index) => {
            option.addEventListener('click', function() {
                // Don't allow selection if already submitted with correct answer
                if (this.querySelector('input[type="radio"]').disabled) return;

                // Just select the answer, don't validate yet
                answerOptions.forEach(opt => {
                    // Reset all styling
                    opt.style.borderColor = '#374151';
                    opt.style.background = 'transparent';
                    opt.style.opacity = '1';
                });

                // Highlight selected option
                this.style.borderColor = '#0ea5e9';
                this.style.background = 'rgba(6, 182, 212, 0.1)';

                // Check the radio button
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                    selectedAnswer = radio.value;
                }

                // Enable add to basket button once an answer is selected
                if (addToBasketBtn) {
                    addToBasketBtn.disabled = false;
                    addToBasketBtn.style.opacity = '1';
                    addToBasketBtn.style.cursor = 'pointer';
                    // Reset button text if it was changed
                    addToBasketBtn.textContent = 'ADD TICKETS TO BASKET';
                    addToBasketBtn.style.background = 'linear-gradient(to right, #0ea5e9, #3b82f6)';
                }

                // Hide any previous feedback
                if (questionFeedback) {
                    questionFeedback.style.display = 'none';
                }
            });
        });
    } else {
        // No question, enable button immediately
        questionAnswered = true;
        if (addToBasketBtn) {
            addToBasketBtn.disabled = false;
        }
    }

    // Add to basket functionality
    if (addToBasketBtn) {
        addToBasketBtn.addEventListener('click', function() {
            if (this.disabled) return;

            const quantity = parseInt(ticketSlider.value);

            // Check if question needs to be answered
            if (skillQuestion && selectedAnswer === null) {
                alert('Please answer the skill question first.');
                return;
            }

            // Validate answer if question exists
            if (skillQuestion && !questionAnswered) {
                const answerOptions = skillQuestion.querySelectorAll('.answer-option');
                const selectedOption = answerOptions[selectedAnswer];
                const isCorrect = selectedOption.dataset.correct === 'true';

                // Highlight the selected answer only
                if (isCorrect) {
                    // Disable all options after correct answer
                    answerOptions.forEach(opt => {
                        opt.style.cursor = 'not-allowed';
                        opt.style.opacity = '0.6';
                        const radio = opt.querySelector('input[type="radio"]');
                        if (radio) radio.disabled = true;
                    });
                    
                    selectedOption.style.borderColor = '#10b981';
                    selectedOption.style.background = 'rgba(16, 185, 129, 0.1)';
                    selectedOption.style.opacity = '1';
                } else {
                    // Keep options enabled for wrong answer
                    selectedOption.style.borderColor = '#ef4444';
                    selectedOption.style.background = 'rgba(239, 68, 68, 0.1)';
                }

                // Show feedback
                if (questionFeedback) {
                    questionFeedback.style.display = 'block';
                    if (isCorrect) {
                        questionFeedback.style.background = 'rgba(16, 185, 129, 0.1)';
                        questionFeedback.style.border = '1px solid #10b981';
                        questionFeedback.style.color = '#10b981';
                        questionFeedback.textContent = '✓ Correct! Adding tickets to basket...';
                        questionAnswered = true;
                    } else {
                        questionFeedback.style.background = 'rgba(239, 68, 68, 0.1)';
                        questionFeedback.style.border = '1px solid #ef4444';
                        questionFeedback.style.color = '#ef4444';
                        questionFeedback.textContent = '✗ Incorrect answer. Please try again';
                        return; // Don't proceed with adding to basket
                    }
                }
            }

            // Add loading state
            this.textContent = 'ADDING TO BASKET...';
            this.disabled = true;

            // Prepare form data
            const formData = new FormData();
            formData.append('action', 'add_competition_to_cart');
            formData.append('competition_id', <?php echo json_encode($competition_id); ?>);
            formData.append('quantity', quantity);
            if (selectedAnswer !== null) {
                formData.append('answer', selectedAnswer);
            }
            formData.append('nonce', '<?php echo wp_create_nonce("competition_nonce"); ?>');

            // Submit to WordPress
            fetch('<?php echo admin_url("admin-ajax.php"); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message and stay on page
                    this.textContent = '✓ TICKETS ADDED!';
                    this.style.background = '#10b981';
                    this.style.borderColor = '#10b981';
                    
                    // Show success notification
                    const successMessage = document.createElement('div');
                    successMessage.style.cssText = `
                        position: fixed;
                        top: 100px;
                        right: 20px;
                        background: #10b981;
                        color: white;
                        padding: 1rem 1.5rem;
                        border-radius: 0.5rem;
                        box-shadow: 0 10px 25px rgba(0,0,0,0.3);
                        z-index: 9999;
                        font-weight: 500;
                        animation: slideIn 0.3s ease-out;
                        max-width: 300px;
                    `;
                    successMessage.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <span style="font-size: 1.2rem;">✓</span>
                            <span>${quantity} ticket${quantity > 1 ? 's' : ''} added to basket!</span>
                        </div>
                    `;
                    
                    document.body.appendChild(successMessage);
                    
                    // Remove success message after 3 seconds
                    setTimeout(() => {
                        successMessage.remove();
                    }, 3000);
                    
                    // Update cart count in header
                    updateCartCount();

                    // Trigger custom event for competition data refresh
                    if (typeof jQuery !== 'undefined') {
                        jQuery(document).trigger('competition_tickets_added');
                    }
                    
                    // Reset button after 2 seconds
                    setTimeout(() => {
                        this.textContent = 'ADD TICKETS TO BASKET';
                        this.style.background = '';
                        this.style.borderColor = '';
                        this.disabled = false;
                    }, 2000);
                } else {
                    // Handle different error types
                    const errorMessage = data.data || 'Error adding to basket. Please try again.';
                    
                    if (errorMessage.toLowerCase().includes('competition has ended')) {
                        // Competition ended while user was on page
                        showCompetitionEndedModal();
                    } else if (errorMessage.toLowerCase().includes('not enough tickets')) {
                        // Not enough tickets available
                        showTicketAvailabilityError(errorMessage);
                    } else {
                        // Generic error
                        alert(errorMessage);
                    }
                    
                    this.textContent = 'ADD TICKETS TO BASKET';
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error adding to basket. Please try again.');
                this.textContent = 'ADD TICKETS TO BASKET';
                this.disabled = false;
            });
        });
    }

    // Show competition ended modal
    function showCompetitionEndedModal() {
        // Update the form to show competition ended state
        const entryForm = document.querySelector('[style*="position: sticky"]');
        if (entryForm) {
            entryForm.innerHTML = `
                <div style="background: rgba(0, 0, 0, 0.8); border: 1px solid rgba(251, 146, 60, 0.5); border-radius: 0.5rem; padding: 2rem; text-align: center;">
                    <div style="margin-bottom: 1.5rem;">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">⏰</div>
                        <h3 style="color: #fb923c; font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem;">Competition Has Ended</h3>
                        <p style="color: #fed7aa; margin-bottom: 1.5rem;">
                            Sorry, this competition ended while you were viewing the page. 
                            The draw will take place soon!
                        </p>
                    </div>
                    
                    <div style="background: rgba(251, 146, 60, 0.1); border: 1px solid rgba(251, 146, 60, 0.3); border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem;">
                        <p style="color: #fb923c; font-weight: 500; margin: 0;">
                            Don't miss out on our other exciting competitions!
                        </p>
                    </div>
                    
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <a href="<?php echo get_permalink(wc_get_page_id('shop')); ?>" 
                           style="background: linear-gradient(to right, #0ea5e9, #3b82f6); color: #000; font-weight: bold; padding: 1rem; border-radius: 0.25rem; text-decoration: none; display: block; transition: all 0.3s;"
                           onmouseover="this.style.background='linear-gradient(to right, #0284c7, #2563eb)';"
                           onmouseout="this.style.background='linear-gradient(to right, #0ea5e9, #3b82f6)';">
                            VIEW ACTIVE COMPETITIONS
                        </a>
                        <button onclick="location.reload();" 
                                style="background: transparent; color: #0ea5e9; border: 1px solid #0ea5e9; padding: 0.75rem; border-radius: 0.25rem; cursor: pointer; font-weight: 500; transition: all 0.3s;"
                                onmouseover="this.style.background='rgba(14, 165, 233, 0.1)';"
                                onmouseout="this.style.background='transparent';">
                            REFRESH PAGE
                        </button>
                    </div>
                </div>
            `;
        }

        // Also update the main content area
        const priceDisplay = document.getElementById('total-price');
        if (priceDisplay) {
            priceDisplay.style.textDecoration = 'line-through';
            priceDisplay.style.opacity = '0.5';
        }
    }

    // Show tooltip
    function showTooltip(element, message) {
        // Remove any existing tooltips
        const existingTooltip = document.querySelector('.ticket-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }

        const tooltip = document.createElement('div');
        tooltip.className = 'ticket-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: #1f2937;
            color: #0ea5e9;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: 1px solid #0ea5e9;
            font-size: 0.875rem;
            white-space: nowrap;
            z-index: 100;
            animation: fadeIn 0.3s;
        `;
        tooltip.textContent = message;

        // Position relative to the element
        document.body.appendChild(tooltip);
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + 'px';
        tooltip.style.top = (rect.top - tooltip.offsetHeight - 10) + 'px';

        // Remove after 3 seconds
        setTimeout(() => tooltip.remove(), 3000);
    }

    // Show ticket availability error
    function showTicketAvailabilityError(message) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        `;
        
        modal.innerHTML = `
            <div style="background: #1f2937; border: 2px solid #ef4444; border-radius: 0.5rem; padding: 2rem; max-width: 400px; text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🎫</div>
                <h3 style="color: #ef4444; font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem;">Not Enough Tickets!</h3>
                <p style="color: #fca5a5; margin-bottom: 1.5rem;">${message}</p>
                <button onclick="this.closest('[style*=fixed]').remove(); document.getElementById('ticket-slider').focus();" 
                        style="background: #ef4444; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 0.25rem; cursor: pointer; font-weight: bold;">
                    ADJUST QUANTITY
                </button>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    // Check competition status periodically
    let statusCheckInterval = setInterval(function() {
        // Only check if competition is active
        if (<?php echo json_encode($is_active); ?>) {
            fetch('<?php echo admin_url("admin-ajax.php"); ?>?action=check_competition_status&competition_id=<?php echo get_the_ID(); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.ended) {
                        clearInterval(statusCheckInterval);
                        showCompetitionEndedModal();
                    }
                })
                .catch(error => console.error('Status check failed:', error));
        }
    }, 30000); // Check every 30 seconds

    // Update countdown timer if competition has end date
    <?php if ($end_date && $is_active) : ?>
    function updateCountdown() {
        const endTime = new Date('<?php echo date('c', strtotime($end_date)); ?>').getTime();
        const now = new Date().getTime();
        const timeLeft = endTime - now;

        if (timeLeft <= 0) {
            // Competition has ended
            clearInterval(countdownInterval);
            showCompetitionEndedModal();
            return;
        }

        // Update any countdown displays on the page
        const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

        // Show warning if less than 5 minutes left
        if (timeLeft < 5 * 60 * 1000 && !document.getElementById('countdown-warning')) {
            const warningBanner = document.createElement('div');
            warningBanner.id = 'countdown-warning';
            warningBanner.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background: linear-gradient(to right, #dc2626, #ef4444);
                color: white;
                text-align: center;
                padding: 1rem;
                font-weight: bold;
                z-index: 9998;
                animation: pulse 2s infinite;
            `;
            warningBanner.innerHTML = `⚠️ HURRY! Competition ends in ${minutes} minutes and ${seconds} seconds! ⚠️`;
            document.body.prepend(warningBanner);
        }

        // Update warning text if it exists
        const warning = document.getElementById('countdown-warning');
        if (warning && timeLeft < 5 * 60 * 1000) {
            warning.innerHTML = `⚠️ HURRY! Competition ends in ${minutes} minutes and ${seconds} seconds! ⚠️`;
        }
    }

    // Update countdown every second
    const countdownInterval = setInterval(updateCountdown, 1000);
    updateCountdown(); // Initial call
    <?php endif; ?>
});

</script>

<style>
/* Custom slider styling */
#ticket-slider {
    -webkit-appearance: none;
    appearance: none;
    background: #374151;
    border-radius: 5px;
    outline: none;
}

/* Pulse animation for countdown warning */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        opacity: 1;
    }
}

/* Fade in animation for tooltips */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#ticket-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #0ea5e9;
    border-radius: 50%;
    cursor: pointer;
}

#ticket-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #0ea5e9;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Responsive design */
@media (max-width: 1024px) {
    .competition-grid {
        grid-template-columns: 1fr !important;
        gap: 2rem !important;
    }

    .sticky {
        position: static !important;
    }
}

/* Entry tab styles */
.entry-tab {
    background: none !important;
    box-shadow: none !important;
}

/* Tab content styles */
.tab-content {
    display: none;
}

.tab-content#paid-entry-content {
    display: block;
}

.entry-tab:hover {
    color: #10b981 !important;
    border-bottom-color: rgba(16, 185, 129, 0.5) !important;
    background: none !important;
    box-shadow: none !important;
}

.entry-tab.active {
    color: #0ea5e9 !important;
    border-bottom-color: #0ea5e9 !important;
    background: none !important;
    box-shadow: none !important;
}

@media (max-width: 768px) {
    h1 {
        font-size: 2rem !important;
    }

    /* Make main layout single column on mobile */
    .product-main-layout {
        display: flex !important;
        flex-direction: column !important;
        gap: 2rem !important;
    }

    .info-badges {
        flex-direction: column !important;
        gap: 0.5rem !important;
    }

    .preset-buttons {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    /* Gallery thumbnails - 3 columns on mobile */
    .gallery-thumbnails {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    /* Preset buttons - 2 columns on mobile */
    .preset-buttons {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    /* Related competitions - single column on mobile */
    .related-competitions {
        grid-template-columns: 1fr !important;
    }
}

/* Slide in animation for success notification */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>

<?php endwhile; ?>

<!-- Lightbox Modal -->
<div id="lightbox" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.9); z-index: 9999; cursor: pointer;" onclick="closeLightbox()">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); max-width: 90%; max-height: 90%;">
        <img id="lightbox-image" src="" alt="" style="max-width: 100%; max-height: 100%; border-radius: 8px;">
    </div>
    <button onclick="closeLightbox()" style="position: absolute; top: 20px; right: 30px; background: none; border: none; color: white; font-size: 2rem; cursor: pointer; z-index: 10000;">&times;</button>
    <button onclick="prevImage()" style="position: absolute; top: 50%; left: 30px; transform: translateY(-50%); background: rgba(255, 255, 255, 0.2); border: none; color: white; font-size: 2rem; cursor: pointer; padding: 10px 15px; border-radius: 4px;">&#8249;</button>
    <button onclick="nextImage()" style="position: absolute; top: 50%; right: 30px; transform: translateY(-50%); background: rgba(255, 255, 255, 0.2); border: none; color: white; font-size: 2rem; cursor: pointer; padding: 10px 15px; border-radius: 4px;">&#8250;</button>
</div>

<script>
// Gallery and lightbox functionality
let galleryImages = <?php echo json_encode($gallery_images ?? array()); ?>;
let currentImageIndex = 0;

function openLightbox(index) {
    if (galleryImages.length === 0) return;

    currentImageIndex = index;
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightbox-image');

    if (lightbox && lightboxImage && galleryImages[index]) {
        lightboxImage.src = galleryImages[index].large;
        lightboxImage.alt = galleryImages[index].alt;
        lightbox.style.display = 'block';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    if (lightbox) {
        lightbox.style.display = 'none';
        document.body.style.overflow = 'auto'; // Restore scrolling
    }
}

function nextImage() {
    if (galleryImages.length === 0) return;
    currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
    const lightboxImage = document.getElementById('lightbox-image');
    if (lightboxImage && galleryImages[currentImageIndex]) {
        lightboxImage.src = galleryImages[currentImageIndex].large;
        lightboxImage.alt = galleryImages[currentImageIndex].alt;
    }
}

function prevImage() {
    if (galleryImages.length === 0) return;
    currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
    const lightboxImage = document.getElementById('lightbox-image');
    if (lightboxImage && galleryImages[currentImageIndex]) {
        lightboxImage.src = galleryImages[currentImageIndex].large;
        lightboxImage.alt = galleryImages[currentImageIndex].alt;
    }
}

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    const lightbox = document.getElementById('lightbox');
    if (lightbox && lightbox.style.display === 'block') {
        if (e.key === 'Escape') {
            closeLightbox();
        } else if (e.key === 'ArrowRight') {
            nextImage();
        } else if (e.key === 'ArrowLeft') {
            prevImage();
        }
    }
});

// Prevent lightbox from closing when clicking on the image
document.getElementById('lightbox-image')?.addEventListener('click', function(e) {
    e.stopPropagation();
});
</script>

<?php get_footer(); ?>


