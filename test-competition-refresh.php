<?php
/**
 * Test Competition Data Refresh
 * 
 * This script tests the competition data refresh functionality
 * Run this by visiting: yoursite.com/test-competition-refresh.php
 */

// Load WordPress
require_once('wp-config.php');

// Prevent direct access from non-admin users
if (!current_user_can('administrator')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🧪 Test Competition Data Refresh</h1>";

// Get a competition to test with
$competitions = get_posts(array(
    'post_type' => 'product',
    'meta_query' => array(
        array(
            'key' => '_is_competition',
            'value' => 'yes'
        )
    ),
    'posts_per_page' => 1
));

if (empty($competitions)) {
    echo "<p style='color: red;'>❌ No competitions found to test with.</p>";
    exit;
}

$competition = $competitions[0];
$competition_id = $competition->ID;

echo "<h2>📊 Testing Competition: " . esc_html($competition->post_title) . " (ID: $competition_id)</h2>";

// Get current competition data
$max_tickets = get_post_meta($competition_id, '_competition_max_tickets', true);
$sold_tickets = get_post_meta($competition_id, '_competition_sold_tickets', true) ?: 0;
$remaining_tickets = max(0, $max_tickets - $sold_tickets);
$progress_percentage = $max_tickets > 0 ? min(100, ($sold_tickets / $max_tickets) * 100) : 0;
$status = get_competition_status_simple($competition_id);

echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>Current Data:</h3>";
echo "<ul>";
echo "<li><strong>Max Tickets:</strong> " . esc_html($max_tickets) . "</li>";
echo "<li><strong>Sold Tickets:</strong> " . esc_html($sold_tickets) . "</li>";
echo "<li><strong>Remaining Tickets:</strong> " . esc_html($remaining_tickets) . "</li>";
echo "<li><strong>Progress:</strong> " . esc_html(round($progress_percentage, 2)) . "%</li>";
echo "<li><strong>Status:</strong> " . esc_html($status) . "</li>";
echo "</ul>";
echo "</div>";

// Test the AJAX endpoint
echo "<h3>🔄 Testing AJAX Endpoint</h3>";

// Simulate the AJAX call
$_POST['competition_id'] = $competition_id;
$_POST['action'] = 'simple_get_competition_data';

// Capture the output
ob_start();
simple_get_competition_data();
$ajax_output = ob_get_clean();

echo "<div style='background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h4>AJAX Response:</h4>";
echo "<pre>" . esc_html($ajax_output) . "</pre>";
echo "</div>";

// Test JavaScript functionality
echo "<h3>🎯 JavaScript Test</h3>";
echo "<div id='test-competition-card' class='competition-card' data-competition-id='$competition_id' style='border: 2px solid #ccc; padding: 20px; margin: 10px 0; border-radius: 5px;'>";
echo "<h4>Test Competition Card</h4>";
echo "<div class='progress-container'>";
echo "<div class='progress-info'>";
echo "<span>" . esc_html($sold_tickets) . " sold</span>";
echo "<span>" . esc_html($remaining_tickets) . " remaining</span>";
echo "</div>";
echo "<div class='progress-bar' style='background: #ddd; height: 10px; border-radius: 5px;'>";
echo "<div class='progress-fill' style='background: #007cba; height: 100%; width: " . esc_attr($progress_percentage) . "%; border-radius: 5px;'></div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<button onclick='testRefresh()' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🔄 Test Refresh</button>";

echo "<script>
function testRefresh() {
    console.log('Testing competition data refresh...');
    
    // Check if jQuery is available
    if (typeof jQuery === 'undefined') {
        alert('❌ jQuery not loaded');
        return;
    }
    
    // Check if AJAX object is available
    if (typeof filmcollectables_ajax === 'undefined') {
        alert('❌ filmcollectables_ajax object not found');
        return;
    }
    
    jQuery.ajax({
        url: filmcollectables_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'simple_get_competition_data',
            competition_id: $competition_id
        },
        success: function(response) {
            console.log('✅ AJAX Success:', response);
            if (response.success && response.data) {
                alert('✅ AJAX working! Data: ' + JSON.stringify(response.data));
                
                // Update the test card
                const data = response.data;
                jQuery('#test-competition-card .progress-info span:first-child').text(data.sold_tickets + ' sold');
                jQuery('#test-competition-card .progress-info span:last-child').text(data.remaining_tickets + ' remaining');
                jQuery('#test-competition-card .progress-fill').css('width', data.progress_percentage + '%');
            } else {
                alert('❌ Invalid response: ' + JSON.stringify(response));
            }
        },
        error: function(xhr, status, error) {
            console.log('❌ AJAX Error:', status, error);
            alert('❌ AJAX Error: ' + status + ' - ' + error);
        }
    });
}
</script>";

echo "<h3>✅ Test Complete</h3>";
echo "<p>If the AJAX test button works, the competition data refresh functionality should now be working on your site!</p>";
echo "<p><strong>Note:</strong> The refresh functionality will automatically update ticket counts every 30 seconds on pages with competition cards, and also when users return to the page after purchasing tickets.</p>";
?>
