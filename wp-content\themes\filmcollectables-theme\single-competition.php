<?php get_header(); ?>

<?php
while (have_posts()) :
    the_post();
    
    // Check if this is a competition product
    $is_competition = get_post_meta(get_the_ID(), '_is_competition', true);
    if ($is_competition !== 'yes') {
        // Redirect to shop if not a competition
        wp_redirect(wc_get_page_permalink('shop'));
        exit;
    }

    // Get WooCommerce product data
    $product = wc_get_product(get_the_ID());
    $ticket_price = $product ? $product->get_price() : '0.25';

    // Get competition meta data
    $max_tickets = get_post_meta(get_the_ID(), '_competition_max_tickets', true);
    $max_tickets = empty($max_tickets) ? 100 : intval($max_tickets);
    $sold_tickets_raw = get_post_meta(get_the_ID(), '_competition_sold_tickets', true);
    $sold_tickets = empty($sold_tickets_raw) ? 0 : intval($sold_tickets_raw);

    // Debug: Let's see what values we're getting
    // error_log('Competition ID: ' . get_the_ID() . ', Max Tickets: ' . $max_tickets . ', Sold Tickets Raw: ' . var_export($sold_tickets_raw, true) . ', Sold Tickets Final: ' . $sold_tickets);
    $status = get_post_meta(get_the_ID(), 'status', true);
    $start_date = get_post_meta(get_the_ID(), 'start_date', true);
    $end_date = get_post_meta(get_the_ID(), 'end_date', true);
    
    // Get instant wins data
    $instant_wins_enabled = get_post_meta(get_the_ID(), '_instant_wins_enabled', true);
    $instant_win_prizes = get_post_meta(get_the_ID(), '_instant_win_prizes', true) ?: array();

    // Get question data from product meta
    $question = get_post_meta(get_the_ID(), '_competition_question', true);
    $answer_1 = get_post_meta(get_the_ID(), '_competition_answer_1', true);
    $answer_2 = get_post_meta(get_the_ID(), '_competition_answer_2', true);
    $answer_3 = get_post_meta(get_the_ID(), '_competition_answer_3', true);
    $correct_answer = get_post_meta(get_the_ID(), '_competition_correct_answer', true);

    // Build current question array
    $current_question = null;
    if ($question && $answer_1 && $answer_2 && $answer_3) {
        $current_question = array(
            'question' => $question,
            'answers' => array($answer_1, $answer_2, $answer_3),
            'correct' => intval($correct_answer) - 1 // Convert 1,2,3 to 0,1,2
        );
    }
    
    // Calculate progress and status
    $progress_percentage = $max_tickets > 0 ? min(100, ($sold_tickets / $max_tickets) * 100) : 0;
    $remaining_tickets = max(0, $max_tickets - $sold_tickets);

    // Get competition status

    $competition_status = get_competition_status_simple(get_the_ID());
    $end_date = get_post_meta(get_the_ID(), '_competition_end_date', true);
    $is_ended = in_array($competition_status, array('awaiting_draw', 'completed'));

    // Product is already loaded above as $product
?>

<!-- Competition Header -->
<div style="background: #000; color: white; text-align: center; padding: 1rem 0;">
    <div class="container">
        <h1 style="font-size: 1.5rem; font-weight: bold; margin: 0;"><?php the_title(); ?></h1>
    </div>
</div>

<!-- Competition Info Bars -->
<div class="competition-info-bars">
    <div class="container" style="max-width: 1200px; padding: 0 1rem;">
        <div class="info-bars-grid">
            <!-- Tickets Available -->
            <div class="info-bar">
                <span class="info-icon">🎫</span>
                <span class="info-text"><?php echo esc_html(number_format($remaining_tickets)); ?> tickets available</span>
            </div>

            <!-- Maximum Tickets Per Person -->
            <div class="info-bar">
                <span class="info-icon">👤</span>
                <span class="info-text">Maximum 999 tickets per person</span>
            </div>

            <!-- Live Draw Date -->
            <div class="info-bar">
                <span class="info-icon">📅</span>
                <span class="info-text">
                    <?php if ($end_date) : ?>
                        Live draw <?php echo esc_html(date('jS F Y', strtotime($end_date))); ?> @ <?php echo esc_html(date('g:i A', strtotime($end_date))); ?>
                    <?php else : ?>
                        Draw date TBA
                    <?php endif; ?>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Main Competition Content -->
<div style="background: #f5f5f5; min-height: 100vh;">
    <div class="container" style="max-width: 1200px; padding: 2rem 1rem;">
        
        <!-- Main Product Section -->
        <div style="display: grid; grid-template-columns: 1fr 500px; gap: 2rem; margin-bottom: 3rem;">
            
            <!-- Left Column - Product Images -->
            <div style="background: white; padding: 2rem; border-radius: 8px;">
                
                <!-- Main Product Image -->
                <div style="margin-bottom: 1rem;">
                    <?php if (has_post_thumbnail()) : ?>
                        <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'large'); ?>" 
                             alt="<?php the_title(); ?>" 
                             style="width: 100%; height: 400px; object-fit: cover; border-radius: 8px;">
                    <?php else : ?>
                        <div style="width: 100%; height: 400px; background: #e5e7eb; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #9ca3af;">
                            No Image Available
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Thumbnail Gallery (placeholder for now) -->
                <div style="display: flex; gap: 0.5rem; justify-content: center;">
                    <?php if (has_post_thumbnail()) : ?>
                        <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'thumbnail'); ?>" 
                             style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px; border: 2px solid #ddd;">
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Right Column - Entry Form and Info -->
            <div style="background: white; padding: 2rem; border-radius: 8px; height: fit-content;">
                
                <!-- Entry Price - Large Display -->
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="font-size: 3rem; font-weight: bold; color: #000; line-height: 1;">£<?php echo esc_html($ticket_price); ?></div>
                    <div style="color: #666; font-size: 1.1rem; margin-top: 0.5rem;">Per Entry</div>
                </div>

                <!-- Countdown Timer -->
                <?php if ($end_date && $competition_status === 'active') : ?>
                    <div style="margin-bottom: 2rem;">
                        <div class="competition-countdown" data-end-date="<?php echo esc_attr($end_date); ?>" style="justify-content: center;">
                            <!-- Countdown will be populated by JavaScript -->
                        </div>
                    </div>
                <?php elseif ($competition_status === 'awaiting_draw') : ?>
                    <div style="margin-bottom: 2rem; text-align: center;">
                        <div class="countdown-expired" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">⏳ Awaiting Draw</div>
                    </div>
                <?php elseif ($competition_status === 'completed') : ?>
                    <div style="margin-bottom: 2rem; text-align: center;">
                        <div class="countdown-expired" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">✅ Competition Completed</div>
                    </div>
                <?php endif; ?>
                
                <!-- Progress Bar -->
                <div style="margin-bottom: 2rem;">
                    <div style="display: flex; justify-content: space-between; font-size: 0.9rem; margin-bottom: 0.5rem;">
                        <span><?php echo esc_html($sold_tickets); ?> entries</span>
                        <span><?php echo esc_html($remaining_tickets); ?> remaining</span>
                    </div>
                    <div style="width: 100%; height: 20px; background: #e5e7eb; border-radius: 10px; overflow: hidden;">
                        <div style="height: 100%; background: linear-gradient(90deg, #fbbf24, #f59e0b); width: <?php echo esc_attr($progress_percentage); ?>%;"></div>
                    </div>
                    <div style="text-align: center; font-size: 0.8rem; color: #666; margin-top: 0.5rem;">
                        <?php echo esc_html(round($progress_percentage, 1)); ?>% Complete
                    </div>
                </div>
                
                <!-- Entry Method Tabs -->
                <div style="margin-bottom: 2rem;">
                    <!-- Tab Headers -->
                    <div style="display: flex; border-radius: 8px; overflow: hidden; border: 1px solid #e5e7eb; margin-bottom: 1rem;">
                        <button class="entry-tab active" data-tab="online" style="flex: 1; padding: 0.75rem; background: #f59e0b; color: white; border: none; font-weight: bold; cursor: pointer; transition: all 0.3s; font-size: 0.85rem;">
                            ONLINE ENTRY
                        </button>
                        <button class="entry-tab" data-tab="postal" style="flex: 1; padding: 0.75rem; background: #d97706; color: white; border: none; font-weight: bold; cursor: pointer; transition: all 0.3s; font-size: 0.85rem;">
                            FREE POSTAL ENTRY
                        </button>
                    </div>

                    <!-- Online Entry Tab Content -->
                    <div class="tab-content active" id="online-tab">
                        <!-- Entry Form -->
                        <?php if (!$is_ended && $remaining_tickets > 0) : ?>
                        <?php if (!is_user_logged_in()) : ?>
                        <div style="text-align: center; padding: 2rem; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin-bottom: 2rem;">
                            <h3 style="color: #856404; margin-bottom: 1rem;">Account Required</h3>
                            <p style="color: #856404; margin-bottom: 1.5rem;">You must be logged in to enter competitions.</p>
                            <div style="display: flex; gap: 1rem; justify-content: center;">
                                <a href="<?php echo esc_url(wc_get_page_permalink('myaccount')); ?>" class="button" style="background: #000; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px;">
                                    Login / Register
                                </a>
                            </div>
                        </div>
                        <?php elseif (!is_user_email_verified(get_current_user_id())) : ?>
                        <!-- Email Verification Required -->
                        <div style="text-align: center; padding: 2rem; background: #fef3c7; border: 1px solid #f59e0b; border-radius: 4px; margin-bottom: 2rem;">
                            <h3 style="color: #92400e; margin-bottom: 1rem;">Email Verification Required</h3>
                            <p style="color: #92400e; margin-bottom: 1rem;">You must verify your email address before entering competitions.</p>
                            <p style="color: #92400e; margin-bottom: 1.5rem;">Please check your email for the verification link. Didn't receive it?</p>
                            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                                <button id="resend-verification-btn" class="button" style="background: #f59e0b; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer;">
                                    Resend Verification Email
                                </button>
                                <a href="<?php echo esc_url(wc_get_endpoint_url('edit-account', '', wc_get_page_permalink('myaccount'))); ?>" class="button" style="background: #6b7280; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px;">
                                    My Account
                                </a>
                            </div>
                        </div>
                        <?php elseif (!Competition_User_Registration::user_meets_age_requirement(get_current_user_id())) : ?>
                        <!-- Age Requirement Not Met -->
                        <div style="text-align: center; padding: 2rem; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 2rem;">
                            <h3 style="color: #721c24; margin-bottom: 1rem;">Age Requirement</h3>
                            <p style="color: #721c24; margin-bottom: 1.5rem;">You must be at least 18 years old to enter competitions. Please update your date of birth in your account settings.</p>
                            <a href="<?php echo esc_url(wc_get_endpoint_url('edit-account', '', wc_get_page_permalink('myaccount'))); ?>" class="button" style="background: #dc3545; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px;">
                                Update Account
                            </a>
                        </div>
                        <?php else : ?>
                        <form id="ticket-purchase-form" style="margin-bottom: 2rem;">
                            <?php wp_nonce_field('purchase_ticket', 'purchase_nonce'); ?>
                            <input type="hidden" name="product_id" value="<?php echo esc_attr(get_the_ID()); ?>">
                            
                            <!-- Question Answer (if question exists) -->
                            <?php if ($current_question) : ?>
                                <div style="margin-bottom: 1.5rem;">
                                    <label style="display: block; font-weight: bold; margin-bottom: 0.5rem; color: #000;">
                                        <?php echo esc_html($current_question['question']); ?>
                                    </label>
                                    <div style="display: grid; gap: 0.5rem;">
                                        <label style="display: flex; align-items: center; padding: 0.75rem; background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 4px; cursor: pointer; transition: all 0.2s;">
                                            <input type="radio" name="answer" value="1" style="margin-right: 0.75rem; cursor: pointer;" required>
                                            <span style="font-size: 0.9rem; cursor: pointer;">A. <?php echo esc_html($current_question['answer_a']); ?></span>
                                        </label>
                                        <label style="display: flex; align-items: center; padding: 0.75rem; background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 4px; cursor: pointer; transition: all 0.2s;">
                                            <input type="radio" name="answer" value="2" style="margin-right: 0.75rem; cursor: pointer;" required>
                                            <span style="font-size: 0.9rem; cursor: pointer;">B. <?php echo esc_html($current_question['answer_b']); ?></span>
                                        </label>
                                        <label style="display: flex; align-items: center; padding: 0.75rem; background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 4px; cursor: pointer; transition: all 0.2s;">
                                            <input type="radio" name="answer" value="3" style="margin-right: 0.75rem; cursor: pointer;" required>
                                            <span style="font-size: 0.9rem; cursor: pointer;">C. <?php echo esc_html($current_question['answer_c']); ?></span>
                                        </label>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Quantity Selector -->
                            <div style="margin-bottom: 1.5rem;">
                                <label style="display: block; font-weight: bold; margin-bottom: 1rem; color: #000;">Number of Entries</label>

                                <!-- Integrated Slider Controls -->
                                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px; border: 1px solid #e9ecef;">

                                    <!-- Slider Row with Integrated Display -->
                                    <div style="display: flex; align-items: center; gap: 1.5rem;">
                                        <button type="button" id="decrease-tickets">
                                            −
                                        </button>

                                        <div style="flex: 1; position: relative;">
                                            <!-- Ticket Display - Fixed Position -->
                                            <div style="text-align: center; margin-bottom: 1rem;">
                                                <div id="ticket-display" style="background: #000; color: white; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.75rem; font-weight: bold; display: inline-block;">
                                                    <span id="ticket-count">5</span> Tickets
                                                </div>
                                            </div>
                                            <input type="range" id="ticket-slider" name="quantity"
                                                   min="1" max="<?php echo min(999, $remaining_tickets); ?>" value="5">
                                        </div>

                                        <button type="button" id="increase-tickets">
                                            +
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <button type="submit" id="add-to-cart-btn" 
                                    style="width: 100%; background: #000; color: white; padding: 1rem; border: none; border-radius: 4px; font-size: 1.1rem; font-weight: bold; cursor: pointer; transition: background 0.2s;">
                                ADD TO BASKET
                            </button>
                        </form>
                        <?php endif; ?>
                        <?php elseif ($competition_status === 'awaiting_draw') : ?>
                            <div style="text-align: center; padding: 2rem; background: #fef3c7; border-radius: 4px; border: 1px solid #f59e0b;">
                                <h3 style="color: #92400e; margin-bottom: 1rem;">⏳ Awaiting Draw</h3>
                                <p style="color: #78350f;">This competition has ended and is awaiting the winner draw. No more entries are being accepted.</p>
                            </div>
                        <?php elseif ($competition_status === 'completed') : ?>
                            <div style="text-align: center; padding: 2rem; background: #f3e8ff; border-radius: 4px; border: 1px solid #8b5cf6;">
                                <h3 style="color: #6b21a8; margin-bottom: 1rem;">✅ Competition Completed</h3>
                                <p style="color: #581c87;">This competition has been completed and the winner has been selected.</p>
                                <?php
                                $winner_name = get_post_meta(get_the_ID(), 'winner_username', true);
                                $winner_ticket = get_post_meta(get_the_ID(), 'winner_ticket_number', true);
                                if ($winner_name && $winner_ticket) : ?>
                                    <div style="margin-top: 1rem; padding: 1rem; background: white; border-radius: 4px;">
                                        <strong>🏆 Winner: <?php echo esc_html($winner_name); ?></strong><br>
                                        <small>Winning Ticket: #<?php echo esc_html($winner_ticket); ?></small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php else : ?>
                            <div style="text-align: center; padding: 2rem; background: #f8f9fa; border-radius: 4px;">
                                <h3 style="color: #dc3545; margin-bottom: 1rem;">Competition Ended</h3>
                                <p style="color: #666;">This competition has ended and is no longer accepting entries.</p>
                            </div>
                        <?php endif; ?>
                        </div>

                        <!-- Postal Entry Tab Content -->
                        <div class="tab-content" id="postal-tab" style="display: none; font-size: 0.9rem; line-height: 1.5; color: #333;">
                            <p style="margin-bottom: 1rem;">
                                You may enter the competition for free by sending your entry on an unenclosed postcard by first or second class post to the Promoter at the following address:
                            </p>

                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 6px; border: 1px solid #dee2e6; margin-bottom: 1rem; text-align: center; font-weight: bold;">
                                FILM COLLECTABLES COMPETITIONS LIMITED<br>
                                Ground Floor, Gallery Building<br>
                                65-69 Dublin Rd<br>
                                Belfast<br>
                                BT2 7HG
                            </div>

                            <p style="margin-bottom: 0.5rem; font-weight: 500;">
                                Please include the following information:
                            </p>

                            <ul style="margin: 0 0 1rem 0; padding-left: 1.2rem; list-style-type: disc;">
                                <li style="margin-bottom: 0.25rem;">your full name</li>
                                <li style="margin-bottom: 0.25rem;">your address</li>
                                <li style="margin-bottom: 0.25rem;">a contact telephone number and email address</li>
                                <li style="margin-bottom: 0.25rem;">the Competition you are entering and your answer to the Competition Question</li>
                            </ul>

                            <p style="margin-bottom: 1rem;">
                                Entries must be received prior to the Closing Date. Postal entries are limited to one entry per person for each competition.
                            </p>

                            <p style="margin: 0;">
                                <a href="<?php echo esc_url(home_url('/terms-and-conditions/')); ?>" style="color: #4f46e5; text-decoration: underline;">Click here for full terms and conditions.</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Description Section -->
        <div style="background: white; padding: 2rem; border-radius: 8px; margin-bottom: 3rem;">
            <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 1.5rem; color: #000; text-transform: uppercase;">
                WIN A <?php echo strtoupper(get_the_title()); ?>
            </h2>
            
            <div style="color: #333; line-height: 1.6; font-size: 1rem;">
                <?php the_content(); ?>
            </div>
            
            <?php if ($current_question) : ?>
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px; margin-top: 2rem; border-left: 4px solid #fbbf24;">
                    <h3 style="font-size: 1.1rem; font-weight: bold; margin-bottom: 1rem; color: #000;">SKILL QUESTION</h3>
                    <p style="color: #333; margin-bottom: 0.5rem;"><?php echo esc_html($current_question['question']); ?></p>
                    <p style="font-size: 0.9rem; color: #666;">You must answer this question correctly to enter the competition.</p>
                </div>
            <?php endif; ?>

            <?php if ($instant_wins_enabled && !empty($instant_win_prizes)) : ?>
                <div style="background: #e8f5e8; padding: 1.5rem; border-radius: 8px; margin-top: 2rem; border-left: 4px solid #10b981;">
                    <h3 style="font-size: 1.1rem; font-weight: bold; margin-bottom: 1rem; color: #000;">🎁 INSTANT WIN PRIZES</h3>
                    <p style="color: #333; margin-bottom: 1rem;">Specific ticket numbers are pre-assigned to instant win prizes. If you buy a winning ticket number, you win instantly!</p>

                    <div style="display: grid; gap: 1rem;">
                        <?php foreach ($instant_win_prizes as $prize) : ?>
                            <div style="background: white; padding: 1.25rem; border-radius: 8px; border: 1px solid #d1fae5;">
                                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 0.75rem;">
                                    <div style="flex: 1;">
                                        <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.25rem;">
                                            <div style="font-weight: bold; color: #000; font-size: 1.1rem;"><?php echo esc_html($prize['name']); ?></div>
                                            <span style="background: <?php echo ($prize['type'] ?? 'site_credit') === 'site_credit' ? '#dbeafe' : '#fef3c7'; ?>; color: <?php echo ($prize['type'] ?? 'site_credit') === 'site_credit' ? '#1e40af' : '#92400e'; ?>; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                                                <?php echo ($prize['type'] ?? 'site_credit') === 'site_credit' ? '💳 Site Credit' : '📦 Physical Prize'; ?>
                                            </span>
                                        </div>
                                        <?php if (!empty($prize['description'])) : ?>
                                            <div style="font-size: 0.9rem; color: #666; margin-top: 0.25rem;"><?php echo esc_html($prize['description']); ?></div>
                                        <?php endif; ?>
                                        <?php if (($prize['type'] ?? 'site_credit') === 'site_credit') : ?>
                                            <div style="font-size: 0.8rem; color: #10b981; margin-top: 0.25rem; font-weight: 500;">
                                                ✓ Instantly added to your wallet
                                            </div>
                                        <?php else : ?>
                                            <div style="font-size: 0.8rem; color: #f59e0b; margin-top: 0.25rem; font-weight: 500;">
                                                📮 Shipped to your address
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div style="font-weight: bold; color: #10b981; font-size: 1.2rem; margin-left: 1rem;">
                                        £<?php echo esc_html(number_format($prize['value'], 2)); ?>
                                    </div>
                                </div>

                                <div style="background: #f0fdf4; padding: 0.75rem; border-radius: 6px; border: 1px solid #bbf7d0;">
                                    <div style="font-size: 0.9rem; font-weight: bold; color: #166534; margin-bottom: 0.5rem;">Winning Ticket Numbers:</div>
                                    <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                        <?php foreach ($prize['ticket_numbers'] as $ticket_num) : ?>
                                            <span style="background: #10b981; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: bold; font-size: 0.9rem;">
                                                #<?php echo esc_html($ticket_num); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div style="background: #f0fdf4; padding: 1rem; border-radius: 6px; margin-top: 1rem; border: 1px solid #bbf7d0;">
                        <p style="font-size: 0.9rem; color: #166534; margin: 0; font-weight: 500;">
                            <strong>How it works:</strong> Buy tickets and if any of your ticket numbers match the winning numbers above, you win that instant prize! After checkout, you'll immediately see if you've won. All tickets are also entered into the main competition draw.
                        </p>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Recent Live Prizes Section -->
        <div style="background: white; padding: 2rem; border-radius: 8px; margin-bottom: 3rem;">
            <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 2rem; color: #000; text-transform: uppercase; text-align: center;">
                RECENT LIVE PRIZES
            </h2>

            <?php
            // Get recent completed competitions with winners
            $recent_winners = wc_get_products(array(
                'limit' => 8,
                'status' => 'publish',
                'meta_query' => array(
                    array(
                        'key' => '_is_competition',
                        'value' => 'yes',
                        'compare' => '='
                    ),
                    array(
                        'key' => 'status',
                        'value' => 'completed',
                        'compare' => '='
                    ),
                    array(
                        'key' => 'winner_username',
                        'value' => '',
                        'compare' => '!='
                    )
                ),
                'orderby' => 'meta_value',
                'meta_key' => '_competition_end_date',
                'order' => 'DESC'
            ));

            if ($recent_winners->have_posts()) :
            ?>
                <div style="display: grid; gap: 1rem;">
                    <?php while ($recent_winners->have_posts()) : $recent_winners->the_post();
                        $winner_name = get_post_meta(get_the_ID(), 'winner_username', true);
                        $winner_ticket = get_post_meta(get_the_ID(), 'winner_ticket_number', true);
                        $draw_date = get_post_meta(get_the_ID(), '_competition_end_date', true);
                        $prize_value = get_post_meta(get_the_ID(), '_price', true);
                    ?>
                        <div style="display: flex; align-items: center; padding: 1rem; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #fbbf24;">
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'thumbnail'); ?>"
                                             style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
                                    <?php endif; ?>
                                    <div style="flex: 1;">
                                        <h4 style="font-size: 0.9rem; font-weight: bold; margin: 0 0 0.25rem 0; color: #000;">
                                            <?php the_title(); ?>
                                        </h4>
                                        <p style="font-size: 0.8rem; color: #666; margin: 0;">
                                            Won by <strong><?php echo esc_html($winner_name); ?></strong>
                                            <?php if ($winner_ticket) : ?>
                                                - Ticket #<?php echo esc_html($winner_ticket); ?>
                                            <?php endif; ?>
                                        </p>
                                        <?php if ($draw_date) : ?>
                                            <p style="font-size: 0.75rem; color: #999; margin: 0.25rem 0 0 0;">
                                                <?php echo date('M j, Y', strtotime($draw_date)); ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div style="background: #fbbf24; color: #000; padding: 0.5rem 1rem; border-radius: 4px; font-weight: bold; font-size: 0.9rem;">
                                £<?php echo esc_html(number_format($prize_value, 0)); ?>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php else : ?>
                <p style="text-align: center; color: #666; font-style: italic;">No recent winners to display.</p>
            <?php endif;
            wp_reset_postdata();
            ?>
        </div>
    </div>
</div>

<?php
endwhile;
?>

<script>
// Competition ticket purchase handler
document.addEventListener('DOMContentLoaded', function() {

    // Ticket slider functionality
    const ticketSlider = document.getElementById('ticket-slider');
    const ticketCount = document.getElementById('ticket-count');
    const decreaseBtn = document.getElementById('decrease-tickets');
    const increaseBtn = document.getElementById('increase-tickets');

    if (ticketSlider && ticketCount) {

        // Function to update display count
        function updateTicketCount() {
            const value = parseInt(ticketSlider.value);
            ticketCount.textContent = value;
        }

        // Update display when slider changes
        ticketSlider.addEventListener('input', updateTicketCount);

        // Initialize count
        updateTicketCount();

        // Decrease button
        decreaseBtn.addEventListener('click', function() {
            const currentValue = parseInt(ticketSlider.value);
            const minValue = parseInt(ticketSlider.min);
            if (currentValue > minValue) {
                ticketSlider.value = currentValue - 1;
                updateTicketCount();
            }
        });

        // Increase button
        increaseBtn.addEventListener('click', function() {
            const currentValue = parseInt(ticketSlider.value);
            const maxValue = parseInt(ticketSlider.max);
            if (currentValue < maxValue) {
                ticketSlider.value = currentValue + 1;
                updateTicketCount();
            }
        });
    }

    // User-friendly message system
    function showMessage(message, type = 'info') {
        // Remove any existing messages
        const existingMessage = document.querySelector('.competition-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `competition-message competition-message-${type}`;
        messageDiv.innerHTML = `
            <div class="competition-message-content">
                <span class="competition-message-text">${message}</span>
                <button class="competition-message-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Insert message at the top of the form area
        const form = document.getElementById('ticket-purchase-form');
        if (form) {
            form.parentNode.insertBefore(messageDiv, form);
        } else {
            // Fallback: insert at top of page content
            const content = document.querySelector('.competition-detail');
            if (content) {
                content.insertBefore(messageDiv, content.firstChild);
            }
        }

        // Auto-remove success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 5000);
        }

        // Scroll to message
        messageDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    const form = document.getElementById('ticket-purchase-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(form);
            const quantity = formData.get('quantity');
            const answer = formData.get('answer');
            const productId = formData.get('product_id');

            // Debug logging
            console.log('Form data:', {
                quantity: quantity,
                answer: answer,
                competitionId: competitionId,
                productId: productId
            });
            
            // Validate answer is selected
            if (!answer) {
                showMessage('Please select an answer to the competition question.', 'error');
                return;
            }

            // Validate product ID
            if (!productId) {
                showMessage('Error: No product ID found for this competition.', 'error');
                return;
            }

            // Disable submit button
            const submitBtn = document.getElementById('add-to-cart-btn');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Adding...';

            // Prepare data for WooCommerce
            const cartData = new FormData();
            cartData.append('action', 'competition_add_to_cart');
            cartData.append('product_id', productId);
            cartData.append('quantity', quantity);
            cartData.append('answer', answer);

            // Add to cart via AJAX
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                body: cartData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                // Check if response is ok
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                
                return response.text(); // Get as text first to see raw response
            })
            .then(responseText => {
                console.log('Raw response:', responseText);
                
                try {
                    const data = JSON.parse(responseText);
                    console.log('Parsed data:', data);
                    
                    if (data.success) {
                        // Success - show message and update cart
                        showMessage('Tickets added to cart successfully!', 'success');

                        // Trigger cart fragments update
                        if (typeof jQuery !== 'undefined') {
                            jQuery('body').trigger('wc_fragment_refresh');
                            // Trigger custom event for competition data refresh
                            jQuery(document).trigger('competition_tickets_added');
                        }
                    } else {
                        showMessage(data.data || 'Unknown error occurred', 'error');
                    }
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.error('Response was:', responseText);
                    showMessage('Error processing server response. Please try again.', 'error');
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                showMessage('Network error. Please check your connection and try again.', 'error');
            })
            .finally(() => {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'ADD TO BASKET';
            });
        });
    }

    // Entry method tab switching
    const entryTabs = document.querySelectorAll('.entry-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    entryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Remove active class from all tabs and contents
            entryTabs.forEach(t => {
                t.classList.remove('active');
                t.style.background = '#d97706';
            });
            tabContents.forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });

            // Add active class to clicked tab and show corresponding content
            this.classList.add('active');
            this.style.background = '#f59e0b';
            document.getElementById(targetTab + '-tab').style.display = 'block';
            document.getElementById(targetTab + '-tab').classList.add('active');
        });
    });
});

// Handle resend verification email
document.addEventListener('DOMContentLoaded', function() {
    const resendBtn = document.getElementById('resend-verification-btn');
    if (resendBtn) {
        resendBtn.addEventListener('click', function() {
            this.disabled = true;
            this.textContent = 'Sending...';
            
            const formData = new FormData();
            formData.append('action', 'resend_verification');
            formData.append('user_email', '<?php echo is_user_logged_in() ? get_current_user()->user_email : ''; ?>');
            formData.append('resend_verification_nonce', '<?php echo wp_create_nonce('resend_verification'); ?>');
            
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Verification email sent successfully! Please check your inbox.');
                    this.textContent = 'Email Sent';
                    this.style.background = '#10b981';
                } else {
                    alert('Failed to send verification email: ' + data.data.message);
                    this.textContent = 'Resend Verification Email';
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while sending the verification email.');
                this.textContent = 'Resend Verification Email';
                this.disabled = false;
            });
        });
    }
});
</script>

<?php get_footer(); ?>
